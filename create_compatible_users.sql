-- Create test users compatible with both current-brobot-1 and daswos-18
-- This script creates users with SHA-256 hashed passwords that current-brobot-1 can use

-- First, let's create a test user with SHA-256 hash (password: "test123")
-- SHA-256 hash of "test123" + "daswos_salt" = 240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9

-- Delete existing test user if exists
DELETE FROM users WHERE username = 'test';

-- Insert test user with SHA-256 hash (compatible with current-brobot-1)
INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance
) VALUES (
    'test', 
    '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', -- SHA-256 hash of "test123" + "daswos_salt"
    '<EMAIL>', 
    'Test User', 
    false, 
    false, 
    50,
    1000
);

-- Create another test user with password "demo123"
-- SHA-256 hash of "demo123" + "daswos_salt" = 8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92

DELETE FROM users WHERE username = 'demo';

INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance
) VALUES (
    'demo', 
    '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92', -- SHA-256 hash of "demo123" + "daswos_salt"
    '<EMAIL>', 
    'Demo User', 
    false, 
    true, 
    75,
    500
);

-- Create a seller user with password "seller123"
-- SHA-256 hash of "seller123" + "daswos_salt" = 5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8

DELETE FROM users WHERE username = 'seller';

INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    identity_verified,
    identity_verification_status
) VALUES (
    'seller', 
    '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8', -- SHA-256 hash of "seller123" + "daswos_salt"
    '<EMAIL>', 
    'Seller User', 
    false, 
    true, 
    90,
    2000,
    true,
    'approved'
);

-- Verify the users were created
SELECT 
    username, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance,
    'Password hash compatible with current-brobot-1' as note
FROM users 
WHERE username IN ('test', 'demo', 'seller', 'admin')
ORDER BY username;

-- Success message
SELECT 'COMPATIBLE TEST USERS CREATED SUCCESSFULLY!' as status,
       'Use these credentials:' as info,
       'test/test123, demo/demo123, seller/seller123, admin/admin123' as credentials;
