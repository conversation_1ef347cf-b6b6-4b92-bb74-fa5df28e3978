<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login - DasWos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 DasWos Login Debug Tool</h1>

        <div class="test-section info">
            <h3>Database Connection Test</h3>
            <button onclick="testConnection()">Test Database Connection</button>
            <div id="connectionResult"></div>
        </div>

        <div class="test-section info">
            <h3>List All Users</h3>
            <button onclick="listUsers()">List All Users</button>
            <div id="usersResult"></div>
        </div>

        <div class="test-section info">
            <h3>Test Login</h3>
            <input type="text" id="testUsername" placeholder="Username" value="test">
            <input type="password" id="testPassword" placeholder="Password" value="test123">
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult"></div>
        </div>

        <div class="test-section info">
            <h3>Create Test User</h3>
            <button onclick="createTestUser()">Create Test User (test/test123)</button>
            <div id="createResult"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://unpkg.com/scrypt-js@3.0.1/scrypt.js"></script>
    <script>
        // Initialize Supabase client
        const SUPABASE_URL = 'https://tqetghhuxpqogxzbwqwe.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRxZXRnaGh1eHBxb2d4emJ3cXdlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODYxOTE0NywiZXhwIjoyMDY0MTk1MTQ3fQ.XlT3knuZpj4Q5FMwNU9iGgRhh7krifp90OwfCrJ4PCo';
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        // Scrypt password hashing
        async function hashPassword(password) {
            try {
                const salt = new Uint8Array(16);
                crypto.getRandomValues(salt);
                const passwordBytes = new TextEncoder().encode(password);
                const hashedBytes = await scrypt(passwordBytes, salt, 16384, 8, 1, 64);
                const hashedHex = Array.from(hashedBytes).map(b => b.toString(16).padStart(2, '0')).join('');
                const saltHex = Array.from(salt).map(b => b.toString(16).padStart(2, '0')).join('');
                return `${hashedHex}.${saltHex}`;
            } catch (error) {
                console.error('Error hashing password:', error);
                throw new Error('Password hashing failed');
            }
        }

        // Function to verify password
        async function verifyPassword(password, storedHash) {
            try {
                if (!storedHash || !storedHash.includes('.')) {
                    return false;
                }
                const [hashedHex, saltHex] = storedHash.split('.');
                if (!hashedHex || !saltHex) {
                    return false;
                }
                const passwordBytes = new TextEncoder().encode(password);
                const saltBytes = new Uint8Array(saltHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));
                const suppliedHashBytes = await scrypt(passwordBytes, saltBytes, 16384, 8, 1, 64);
                const storedHashBytes = new Uint8Array(hashedHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));
                if (storedHashBytes.length !== suppliedHashBytes.length) {
                    return false;
                }
                let result = 0;
                for (let i = 0; i < storedHashBytes.length; i++) {
                    result |= storedHashBytes[i] ^ suppliedHashBytes[i];
                }
                return result === 0;
            } catch (error) {
                console.error('Error verifying password:', error);
                return false;
            }
        }

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            try {
                const { data, error } = await supabase
                    .from('users')
                    .select('count', { count: 'exact', head: true });

                if (error) throw error;

                resultDiv.innerHTML = `<div class="success">✅ Connection successful! Database is accessible.</div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
            }
        }

        async function listUsers() {
            const resultDiv = document.getElementById('usersResult');
            try {
                const { data: users, error } = await supabase
                    .from('users')
                    .select('id, username, email, full_name, password, is_admin, is_seller, created_at')
                    .order('created_at', { ascending: false });

                if (error) throw error;

                if (users && users.length > 0) {
                    let html = `<div class="success">✅ Found ${users.length} users:</div><pre>`;
                    users.forEach(user => {
                        html += `Username: ${user.username}\n`;
                        html += `Email: ${user.email}\n`;
                        html += `Full Name: ${user.full_name}\n`;
                        html += `Password Hash: ${user.password ? user.password.substring(0, 50) + '...' : 'No password'}\n`;
                        html += `Admin: ${user.is_admin}, Seller: ${user.is_seller}\n`;
                        html += `Created: ${user.created_at}\n`;
                        html += `---\n`;
                    });
                    html += '</pre>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ No users found in database</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error listing users: ${error.message}</div>`;
            }
        }

        async function testLogin() {
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                // Get user by username
                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .eq('username', username)
                    .limit(1);

                if (error) throw error;

                if (users && users.length > 0) {
                    const user = users[0];

                    // Test password verification
                    const passwordValid = await verifyPassword(password, user.password);

                    if (passwordValid) {
                        resultDiv.innerHTML = `<div class="success">✅ Login successful for user: ${user.username}</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Password verification failed for user: ${user.username}<br>
                        Stored hash: ${user.password}<br>
                        Hash format: ${user.password ? (user.password.includes('.') ? 'Scrypt format' : 'Unknown format') : 'No password'}</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ User '${username}' not found</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Login test failed: ${error.message}</div>`;
            }
        }

        async function createTestUser() {
            const resultDiv = document.getElementById('createResult');
            try {
                // Hash the password
                const hashedPassword = await hashPassword('test123');

                // Create test user
                const { data: newUser, error } = await supabase
                    .from('users')
                    .insert({
                        username: 'test',
                        email: '<EMAIL>',
                        full_name: 'Test User',
                        password: hashedPassword,
                        is_admin: false,
                        is_seller: false,
                        trust_score: 30
                    })
                    .select()
                    .single();

                if (error) throw error;

                resultDiv.innerHTML = `<div class="success">✅ Test user created successfully!<br>
                Username: test<br>
                Password: test123<br>
                Hash: ${hashedPassword}</div>`;
            } catch (error) {
                if (error.message.includes('duplicate key')) {
                    resultDiv.innerHTML = `<div class="info">ℹ️ Test user already exists. Try logging in with username 'test' and password 'test123'</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error creating test user: ${error.message}</div>`;
                }
            }
        }
    </script>
</body>
</html>
